{"builtTestProducts": [], "copyCommands": {"/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/PrivacyInfo.xcprivacy": {"inputs": [{"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/PrivacyInfo.xcprivacy"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/PrivacyInfo.xcprivacy"}]}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/ZLPhotoBrowser.bundle": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/ZLPhotoBrowser.bundle"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/ZLPhotoBrowser.bundle"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources", "importPath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt"}, {"kind": "virtual", "name": "<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources"}], "isLibrary": true, "moduleName": "ZLPhotoBrowser", "moduleOutputPath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules/ZLPhotoBrowser.swiftmodule", "objects": ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageDismissAnimatedTransition.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewDismissInteractiveTransition.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewAnimatedTransition.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewPopInteractiveTransition.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomCamera.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAdjustSlider.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickerView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickertState.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageViewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipOverlayView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageViewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditToolCells.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditVideoViewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditorManager.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFilter.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageStickerView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLInputTextViewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPaths.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLTextStickerView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/AVCaptureDevice+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Array+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bool+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bundle+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/CGFloat+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Cell+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/NSError+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHAsset+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHPhotoLibrary+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Runtime+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/String+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIColor+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIFont+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIGraphicsImageRenderer+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIImage+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIScrollView+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIView+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIViewController+ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAddPhotoCell.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListCell.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListModel.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAnimationUtils.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraCell.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraConfiguration.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCollectionViewFlowLayout.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomAlertProtocol.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageConfiguration.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEmbedAlbumListView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEnlargeButton.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFetchImageOperation.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLGeneralDefine.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageNavController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLLanguageDefine.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLNoAuthTipsView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration+Chaining.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoManager.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoModel.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPicker.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewCell.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewSheet.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration+Chaining.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressHUD.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressView.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLResultModel.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailPhotoCell.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailViewController.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLVideoManager.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLWeakProxy.swift.o", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g"], "outputFileMapPath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules/ZLPhotoBrowser.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"ZLPhotoBrowser": ["/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ZLPhotoBrowser", "-incremental", "-c", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-F", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-driver-use-frontend-path", "/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"ZLPhotoBrowser": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift"}, {"kind": "file", "name": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt"}}}