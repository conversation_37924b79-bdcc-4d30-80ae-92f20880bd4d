{"": {"swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/master.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageDismissAnimatedTransition.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageDismissAnimatedTransition.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageDismissAnimatedTransition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageDismissAnimatedTransition.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewDismissInteractiveTransition.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewDismissInteractiveTransition.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewDismissInteractiveTransition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewDismissInteractiveTransition.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewAnimatedTransition.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewAnimatedTransition.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewAnimatedTransition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewAnimatedTransition.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewPopInteractiveTransition.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewPopInteractiveTransition.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewPopInteractiveTransition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewPopInteractiveTransition.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomCamera.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomCamera.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomCamera~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomCamera.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAdjustSlider.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAdjustSlider.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAdjustSlider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAdjustSlider.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickerView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickerView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickerView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickertState.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickertState.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickertState~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLBaseStickertState.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageViewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageViewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipImageViewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipOverlayView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipOverlayView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipOverlayView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLClipOverlayView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageViewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageViewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageViewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditToolCells.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditToolCells.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditToolCells~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditToolCells.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditVideoViewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditVideoViewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditVideoViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditVideoViewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditorManager.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditorManager.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditorManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditorManager.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFilter.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFilter.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFilter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFilter.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageStickerView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageStickerView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageStickerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageStickerView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLInputTextViewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLInputTextViewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLInputTextViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLInputTextViewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPaths.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPaths.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPaths~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPaths.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLTextStickerView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLTextStickerView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLTextStickerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLTextStickerView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/AVCaptureDevice+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/AVCaptureDevice+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/AVCaptureDevice+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/AVCaptureDevice+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Array+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Array+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Array+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Array+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bool+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bool+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bool+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bool+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bundle+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bundle+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bundle+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Bundle+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/CGFloat+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/CGFloat+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/CGFloat+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/CGFloat+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Cell+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Cell+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Cell+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Cell+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/NSError+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/NSError+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/NSError+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/NSError+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHAsset+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHAsset+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHAsset+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHAsset+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHPhotoLibrary+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHPhotoLibrary+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHPhotoLibrary+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/PHPhotoLibrary+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Runtime+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Runtime+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Runtime+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/Runtime+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/String+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/String+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/String+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/String+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIColor+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIColor+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIColor+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIColor+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIFont+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIFont+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIFont+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIFont+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIGraphicsImageRenderer+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIGraphicsImageRenderer+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIGraphicsImageRenderer+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIGraphicsImageRenderer+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIImage+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIImage+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIImage+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIImage+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIScrollView+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIScrollView+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIScrollView+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIScrollView+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIView+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIView+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIView+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIView+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIViewController+ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIViewController+ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIViewController+ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/UIViewController+ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAddPhotoCell.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAddPhotoCell.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAddPhotoCell~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAddPhotoCell.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListCell.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListCell.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListCell~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListCell.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListModel.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListModel.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAlbumListModel.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAnimationUtils.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAnimationUtils.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAnimationUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLAnimationUtils.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraCell.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraCell.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraCell~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraCell.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraConfiguration.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraConfiguration.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCameraConfiguration.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCollectionViewFlowLayout.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCollectionViewFlowLayout.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCollectionViewFlowLayout~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCollectionViewFlowLayout.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomAlertProtocol.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomAlertProtocol.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomAlertProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLCustomAlertProtocol.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageConfiguration.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageConfiguration.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEditImageConfiguration.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEmbedAlbumListView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEmbedAlbumListView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEmbedAlbumListView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEmbedAlbumListView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEnlargeButton.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEnlargeButton.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEnlargeButton~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLEnlargeButton.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFetchImageOperation.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFetchImageOperation.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFetchImageOperation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLFetchImageOperation.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLGeneralDefine.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLGeneralDefine.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLGeneralDefine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLGeneralDefine.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageNavController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageNavController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageNavController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImageNavController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLImagePreviewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLLanguageDefine.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLLanguageDefine.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLLanguageDefine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLLanguageDefine.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLNoAuthTipsView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLNoAuthTipsView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLNoAuthTipsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLNoAuthTipsView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration+Chaining.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration+Chaining.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration+Chaining~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration+Chaining.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoConfiguration.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoManager.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoManager.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoManager.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoModel.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoModel.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoModel.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPicker.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPicker.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPicker~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPicker.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewCell.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewCell.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewCell~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewCell.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewSheet.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewSheet.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewSheet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoPreviewSheet.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration+Chaining.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration+Chaining.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration+Chaining~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration+Chaining.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoUIConfiguration.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressHUD.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressHUD.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressHUD~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressHUD.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressView.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressView.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLProgressView.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLResultModel.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLResultModel.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLResultModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLResultModel.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailPhotoCell.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailPhotoCell.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailPhotoCell~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailPhotoCell.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailViewController.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailViewController.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLThumbnailViewController.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLVideoManager.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLVideoManager.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLVideoManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLVideoManager.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLWeakProxy.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLWeakProxy.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLWeakProxy~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLWeakProxy.swiftdeps"}, "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/resource_bundle_accessor.d", "object": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/resource_bundle_accessor.swiftdeps"}}