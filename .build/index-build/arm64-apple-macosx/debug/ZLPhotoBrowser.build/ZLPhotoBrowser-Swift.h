// Generated by Apple Swift version 6.1 effective-5.10 (swiftlang-6.1.0.110.21 clang-1700.0.13.3)
#ifndef ZLPHOTOBROWSER_SWIFT_H
#define ZLPHOTOBROWSER_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import AVFoundation;
@import CoreFoundation;
@import CoreMedia;
@import Foundation;
@import ObjectiveC;
@import Photos;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="ZLPhotoBrowser",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

/// Adjust slider type
typedef SWIFT_ENUM(NSInteger, ZLAdjustSliderType, closed) {
  ZLAdjustSliderTypeVertical = 0,
  ZLAdjustSliderTypeHorizontal = 1,
};

SWIFT_CLASS("_TtC14ZLPhotoBrowser16ZLAlbumListModel")
@interface ZLAlbumListModel : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum CaptureSessionPreset : NSInteger;
enum FocusMode : NSInteger;
enum ExposureMode : NSInteger;
enum VideoExportType : NSInteger;
enum DevicePosition : NSInteger;
SWIFT_CLASS("_TtC14ZLPhotoBrowser21ZLCameraConfiguration")
@interface ZLCameraConfiguration : NSObject
/// Allow taking photos in the camera (Need allowSelectImage to be true). Defaults to true.
@property (nonatomic) BOOL allowTakePhoto;
/// Allow recording in the camera (Need allowSelectVideo to be true). Defaults to true.
@property (nonatomic) BOOL allowRecordVideo;
/// Minimum recording duration. Defaults to 0.
@property (nonatomic) NSInteger minRecordDuration;
/// Maximum recording duration. Defaults to 20, minimum is 1.
@property (nonatomic) NSInteger maxRecordDuration;
/// Indicates whether the video flowing through the connection should be mirrored about its vertical axis.
@property (nonatomic) BOOL isVideoMirrored;
/// Video resolution. Defaults to hd1920x1080.
@property (nonatomic) enum CaptureSessionPreset sessionPreset;
/// Camera focus mode. Defaults to continuousAutoFocus
@property (nonatomic) enum FocusMode focusMode;
/// Camera exposure mode. Defaults to continuousAutoExposure
@property (nonatomic) enum ExposureMode exposureMode;
/// Camera flahs switch. Defaults to true.
@property (nonatomic) BOOL showFlashSwitch;
/// Whether to support switch camera. Defaults to true.
@property (nonatomic) BOOL allowSwitchCamera;
/// Flag to enable tap-to-record functionality. Default is false.
/// Note: This property is prioritized lower than <code>allowTakePhoto</code>.
/// If <code>allowTakePhoto</code> is true, <code>tapToRecordVideo</code> will be ignored.
@property (nonatomic) BOOL tapToRecordVideo;
/// Enable the use of wide cameras (e.g., .builtInTripleCamera, .builtInDualWideCamera, .builtInDualCamera).
/// Only available on iOS 13.0 and higher, defaults to false.
@property (nonatomic) BOOL enableWideCameras SWIFT_AVAILABILITY(ios,introduced=13.0);
/// Video stabilization mode. Defaults to .off.
@property (nonatomic) AVCaptureVideoStabilizationMode videoStabilizationMode;
/// Video export format for recording video and editing video. Defaults to mov.
@property (nonatomic) enum VideoExportType videoExportType;
/// The default camera position after entering the camera. Defaults to back.
@property (nonatomic) enum DevicePosition devicePosition;
/// The codecs for video capture. Defaults to .h264
@property (nonatomic) AVVideoCodecType _Nonnull videoCodecType SWIFT_AVAILABILITY(ios,introduced=11.0);
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ZLCameraConfiguration (SWIFT_EXTENSION(ZLPhotoBrowser))
@end

typedef SWIFT_ENUM(NSInteger, CaptureSessionPreset, closed) {
  CaptureSessionPresetCif352x288 = 0,
  CaptureSessionPresetVga640x480 = 1,
  CaptureSessionPresetHd1280x720 = 2,
  CaptureSessionPresetHd1920x1080 = 3,
  CaptureSessionPresetHd4K3840x2160 = 4,
  CaptureSessionPresetPhoto = 5,
};

typedef SWIFT_ENUM(NSInteger, FocusMode, closed) {
  FocusModeAutoFocus = 0,
  FocusModeContinuousAutoFocus = 1,
};

typedef SWIFT_ENUM(NSInteger, ExposureMode, closed) {
  ExposureModeAutoExpose = 0,
  ExposureModeContinuousAutoExposure = 1,
};

typedef SWIFT_ENUM(NSInteger, VideoExportType, closed) {
  VideoExportTypeMov = 0,
  VideoExportTypeMp4 = 1,
};

typedef SWIFT_ENUM(NSInteger, DevicePosition, closed) {
  DevicePositionBack = 0,
  DevicePositionFront = 1,
};

@interface ZLCameraConfiguration (SWIFT_EXTENSION(ZLPhotoBrowser))
- (ZLCameraConfiguration * _Nonnull)allowTakePhoto:(BOOL)value;
- (ZLCameraConfiguration * _Nonnull)allowRecordVideo:(BOOL)value;
- (ZLCameraConfiguration * _Nonnull)minRecordDuration:(NSInteger)duration;
- (ZLCameraConfiguration * _Nonnull)maxRecordDuration:(NSInteger)duration;
- (ZLCameraConfiguration * _Nonnull)sessionPreset:(enum CaptureSessionPreset)sessionPreset;
- (ZLCameraConfiguration * _Nonnull)focusMode:(enum FocusMode)mode;
- (ZLCameraConfiguration * _Nonnull)exposureMode:(enum ExposureMode)mode;
- (ZLCameraConfiguration * _Nonnull)showFlashSwitch:(BOOL)value;
- (ZLCameraConfiguration * _Nonnull)allowSwitchCamera:(BOOL)value;
- (ZLCameraConfiguration * _Nonnull)videoExportType:(enum VideoExportType)type;
- (ZLCameraConfiguration * _Nonnull)devicePosition:(enum DevicePosition)position;
- (ZLCameraConfiguration * _Nonnull)videoCodecType:(AVVideoCodecType _Nonnull)type SWIFT_AVAILABILITY(ios,introduced=11.0);
- (ZLCameraConfiguration * _Nonnull)tapToRecordVideo:(BOOL)value;
- (ZLCameraConfiguration * _Nonnull)enableWideCameras:(BOOL)value SWIFT_AVAILABILITY(ios,introduced=13.0);
- (ZLCameraConfiguration * _Nonnull)videoStabilizationMode:(AVCaptureVideoStabilizationMode)value;
@end

typedef SWIFT_ENUM(NSInteger, ZLFilterType, closed) {
  ZLFilterTypeNormal = 0,
  ZLFilterTypeChrome = 1,
  ZLFilterTypeFade = 2,
  ZLFilterTypeInstant = 3,
  ZLFilterTypeProcess = 4,
  ZLFilterTypeTransfer = 5,
  ZLFilterTypeTone = 6,
  ZLFilterTypeLinear = 7,
  ZLFilterTypeSepia = 8,
  ZLFilterTypeMono = 9,
  ZLFilterTypeNoir = 10,
  ZLFilterTypeTonal = 11,
};

SWIFT_PROTOCOL("_TtP14ZLPhotoBrowser32ZLImagePreviewControllerDelegate_")
@protocol ZLImagePreviewControllerDelegate
@end

/// Provide an image sticker container view that conform to this protocol must be a subclass of UIView
/// 必须是UIView的子类遵循这个协议
SWIFT_PROTOCOL("_TtP14ZLPhotoBrowser31ZLImageStickerContainerDelegate_")
@protocol ZLImageStickerContainerDelegate
@property (nonatomic, copy) void (^ _Nullable hideBlock)(void);
@end

typedef SWIFT_ENUM(NSInteger, ZLLanguageType, closed) {
  ZLLanguageTypeSystem = 0,
  ZLLanguageTypeChineseSimplified = 1,
  ZLLanguageTypeChineseTraditional = 2,
  ZLLanguageTypeEnglish = 3,
  ZLLanguageTypeJapanese = 4,
  ZLLanguageTypeFrench = 5,
  ZLLanguageTypeGerman = 6,
  ZLLanguageTypeRussian = 7,
  ZLLanguageTypeVietnamese = 8,
  ZLLanguageTypeKorean = 9,
  ZLLanguageTypeMalay = 10,
  ZLLanguageTypeItalian = 11,
  ZLLanguageTypeIndonesian = 12,
  ZLLanguageTypePortuguese = 13,
  ZLLanguageTypeSpanish = 14,
  ZLLanguageTypeTurkish = 15,
  ZLLanguageTypeArabic = 16,
  ZLLanguageTypeDutch = 17,
};

typedef SWIFT_ENUM(NSInteger, ZLNoAuthorityType, closed) {
  ZLNoAuthorityTypeLibrary = 0,
  ZLNoAuthorityTypeCamera = 1,
  ZLNoAuthorityTypeMicrophone = 2,
};

typedef SWIFT_ENUM(NSInteger, ZLPhotoBrowserStyle, closed) {
/// The album list is embedded in the navigation of the thumbnail interface, click the drop-down display.
  ZLPhotoBrowserStyleEmbedAlbumList = 0,
/// The display relationship between the album list and the thumbnail interface is push.
  ZLPhotoBrowserStyleExternalAlbumList = 1,
};

@class PHAsset;
SWIFT_CLASS("_TtC14ZLPhotoBrowser20ZLPhotoConfiguration")
@interface ZLPhotoConfiguration : NSObject
+ (ZLPhotoConfiguration * _Nonnull)default SWIFT_WARN_UNUSED_RESULT;
+ (void)resetConfiguration;
/// Anything superior than 1 will enable the multiple selection feature. Defaults to 9.
@property (nonatomic) NSInteger maxSelectCount;
/// A count for video max selection. Defaults to 0.
/// warning:
/// Only valid in mix selection mode. (i.e. allowMixSelect = true)
@property (nonatomic) NSInteger maxVideoSelectCount;
/// A count for video min selection. Defaults to 0.
/// warning:
/// Only valid in mix selection mode. (i.e. allowMixSelect = true)
@property (nonatomic) NSInteger minVideoSelectCount;
/// Whether photos and videos can be selected together. Defaults to true.
/// If set to false, only one video can be selected. Defaults to true.
@property (nonatomic) BOOL allowMixSelect;
/// Preview selection max preview count, if the value is zero, only show <code>Camera</code>, <code>Album</code>, <code>Cancel</code> buttons. Defaults to 20.
@property (nonatomic) NSInteger maxPreviewCount;
/// The index of the first selected image, and the indices of subsequently selected images are incremented based on this value. Defaults to 1.
@property (nonatomic) NSInteger initialIndex;
/// If set to false, gif and livephoto cannot be selected either. Defaults to true.
@property (nonatomic) BOOL allowSelectImage;
@property (nonatomic) BOOL allowSelectVideo;
/// If set to true, videos on iCloud will be downloaded before selection. Defaults to false.
/// note:
/// The download timeout time is <code>ZLPhotoConfiguration.default().timeout</code>.
@property (nonatomic) BOOL downloadVideoBeforeSelecting;
/// Allow select Gif, it only controls whether it is displayed in Gif form.
/// If value is false, the Gif logo is not displayed. Defaults to true.
@property (nonatomic) BOOL allowSelectGif;
/// Allow select LivePhoto, it only controls whether it is displayed in LivePhoto form.
/// If value is false, the LivePhoto logo is not displayed. Defaults to false.
@property (nonatomic) BOOL allowSelectLivePhoto;
/// Allow take photos in the album. Defaults to true.
/// warning:
/// If allowTakePhoto and allowRecordVideo are both false, it will not be displayed.
@property (nonatomic) BOOL allowTakePhotoInLibrary;
/// Whether to callback directly after taking a photo. Defaults to false.
@property (nonatomic) BOOL callbackDirectlyAfterTakingPhoto;
@property (nonatomic) BOOL allowEditImage;
@property (nonatomic) BOOL allowEditVideo;
/// After selecting a image/video in the thumbnail interface, enter the editing interface directly. Defaults to false.
/// <ul>
///   <li>
///     discussion: Editing image is only valid when allowEditImage is true and maxSelectCount is 1.
///     Editing video is only valid when allowEditVideo is true and maxSelectCount is 1.
///   </li>
/// </ul>
@property (nonatomic) BOOL editAfterSelectThumbnailImage;
/// Only valid when allowMixSelect is false and allowEditVideo is true. Defaults to true.
/// Just like the Wechat-Timeline selection style. If you want to crop the video after select thumbnail under allowMixSelect = true, please use <em>editAfterSelectThumbnailImage</em>.
@property (nonatomic) BOOL cropVideoAfterSelectThumbnail;
/// Save the edited image to the album after editing. Defaults to true.
@property (nonatomic) BOOL saveNewImageAfterEdit;
/// If true, you can slide select photos in album. Defaults to true.
@property (nonatomic) BOOL allowSlideSelect;
/// When slide select is active, will auto scroll to top or bottom when your finger at the top or bottom. Defaults to true.
@property (nonatomic) BOOL autoScrollWhenSlideSelectIsActive;
/// The max speed (pt/s) of auto scroll. Defaults to 600.
@property (nonatomic) CGFloat autoScrollMaxSpeed;
/// If true, you can drag select photo when preview selection style. Defaults to false.
@property (nonatomic) BOOL allowDragSelect;
/// Allow select full image. Defaults to true.
@property (nonatomic) BOOL allowSelectOriginal;
/// Always return the original photo.
/// warning:
/// Only valid when <code>allowSelectOriginal = false</code>, Defaults to false.
@property (nonatomic) BOOL alwaysRequestOriginal;
/// Whether to show the total size of selected photos when selecting the original image. Defaults to true.
/// note:
/// The framework uses a conversion ratio of 1KB=1024Byte, while the system album uses 1KB=1000Byte, so the displayed photo size within the framework will be smaller than the size in the system album.
@property (nonatomic) BOOL showOriginalSizeWhenSelectOriginal;
/// Allow access to the preview large image interface (That is, whether to allow access to the large image interface after clicking the thumbnail image). Defaults to true.
@property (nonatomic) BOOL allowPreviewPhotos;
/// Whether to show the preview button (i.e. the preview button in the lower left corner of the thumbnail interface). Defaults to true.
@property (nonatomic) BOOL showPreviewButtonInAlbum;
/// Whether to display the selected count on the button. Defaults to true.
@property (nonatomic) BOOL showSelectCountOnDoneBtn;
/// In single selection mode, whether to display the selection button. Defaults to false.
@property (nonatomic) BOOL showSelectBtnWhenSingleSelect;
/// Display the index of the selected photos. Defaults to true.
@property (nonatomic) BOOL showSelectedIndex;
/// Maximum cropping time when editing video, unit: second. Defaults to 10.
@property (nonatomic) NSInteger maxEditVideoTime;
/// Allow to choose the maximum duration of the video. Defaults to 120.
@property (nonatomic) NSInteger maxSelectVideoDuration;
/// Allow to choose the minimum duration of the video. Defaults to 0.
@property (nonatomic) NSInteger minSelectVideoDuration;
/// Allow to choose the maximum data size of the video. Defaults to infinite.
@property (nonatomic) CGFloat maxSelectVideoDataSize;
/// Allow to choose the minimum data size of the video. Defaults to 0 KB.
@property (nonatomic) CGFloat minSelectVideoDataSize;
/// Whether to use custom camera. Defaults to true.
@property (nonatomic) BOOL useCustomCamera;
/// The configuration for camera.
@property (nonatomic, strong) ZLCameraConfiguration * _Nonnull cameraConfiguration;
/// This block will be called before selecting an image, the developer can first determine whether the asset is allowed to be selected.
/// Only control whether it is allowed to be selected, and will not affect the selection logic in the framework.
/// <ul>
///   <li>
///     Tips: If the choice is not allowed, the developer can toast prompt the user for relevant information.
///   </li>
/// </ul>
@property (nonatomic, copy) BOOL (^ _Nullable canSelectAsset)(PHAsset * _Nonnull);
/// This block will be called when selecting an asset.
@property (nonatomic, copy) void (^ _Nullable didSelectAsset)(PHAsset * _Nonnull);
/// This block will be called when cancel selecting an asset.
@property (nonatomic, copy) void (^ _Nullable didDeselectAsset)(PHAsset * _Nonnull);
/// This block will be called when clicking the camera button in the library.
@property (nonatomic, copy) BOOL (^ _Nullable canEnterCamera)(void);
/// The maximum number of frames for GIF images. To avoid crashes due to memory spikes caused by loading GIF images with too many frames, it is recommended that this value is not too large. Defaults to 50.
@property (nonatomic) NSInteger maxFrameCountForGIF;
/// Callback after the no authority alert dismiss.
@property (nonatomic, copy) void (^ _Nullable noAuthorityCallback)(enum ZLNoAuthorityType);
/// Allow user to provide a custom alert while presenting ZLPhotoPreviewSheet with the authority is denied.
@property (nonatomic, copy) void (^ _Nullable customAlertWhenNoAuthority)(enum ZLNoAuthorityType);
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ZLPhotoConfiguration (SWIFT_EXTENSION(ZLPhotoBrowser))
- (ZLPhotoConfiguration * _Nonnull)maxSelectCount:(NSInteger)count;
- (ZLPhotoConfiguration * _Nonnull)maxVideoSelectCount:(NSInteger)count;
- (ZLPhotoConfiguration * _Nonnull)minVideoSelectCount:(NSInteger)count;
- (ZLPhotoConfiguration * _Nonnull)allowMixSelect:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)maxPreviewCount:(NSInteger)count;
- (ZLPhotoConfiguration * _Nonnull)initialIndex:(NSInteger)index;
- (ZLPhotoConfiguration * _Nonnull)allowSelectImage:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowSelectVideo:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)downloadVideoBeforeSelecting:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowSelectGif:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowSelectLivePhoto:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowTakePhotoInLibrary:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)callbackDirectlyAfterTakingPhoto:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowEditImage:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowEditVideo:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)editAfterSelectThumbnailImage:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)cropVideoAfterSelectThumbnail:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)saveNewImageAfterEdit:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowSlideSelect:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)autoScrollWhenSlideSelectIsActive:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)autoScrollMaxSpeed:(CGFloat)speed;
- (ZLPhotoConfiguration * _Nonnull)allowDragSelect:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowSelectOriginal:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)alwaysRequestOriginal:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)allowPreviewPhotos:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)showPreviewButtonInAlbum:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)showSelectCountOnDoneBtn:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)showSelectBtnWhenSingleSelect:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)showSelectedIndex:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)maxEditVideoTime:(NSInteger)second;
- (ZLPhotoConfiguration * _Nonnull)maxSelectVideoDuration:(NSInteger)duration;
- (ZLPhotoConfiguration * _Nonnull)minSelectVideoDuration:(NSInteger)duration;
- (ZLPhotoConfiguration * _Nonnull)maxSelectVideoDataSize:(CGFloat)size;
- (ZLPhotoConfiguration * _Nonnull)minSelectVideoDataSize:(CGFloat)size;
- (ZLPhotoConfiguration * _Nonnull)useCustomCamera:(BOOL)value;
- (ZLPhotoConfiguration * _Nonnull)cameraConfiguration:(ZLCameraConfiguration * _Nonnull)configuration;
- (ZLPhotoConfiguration * _Nonnull)canSelectAsset:(BOOL (^ _Nullable)(PHAsset * _Nonnull))block;
- (ZLPhotoConfiguration * _Nonnull)didSelectAsset:(void (^ _Nullable)(PHAsset * _Nonnull))block;
- (ZLPhotoConfiguration * _Nonnull)didDeselectAsset:(void (^ _Nullable)(PHAsset * _Nonnull))block;
- (ZLPhotoConfiguration * _Nonnull)canEnterCamera:(BOOL (^ _Nullable)(void))block;
- (ZLPhotoConfiguration * _Nonnull)maxFrameCountForGIF:(NSInteger)frameCount;
- (ZLPhotoConfiguration * _Nonnull)noAuthorityCallback:(void (^ _Nullable)(enum ZLNoAuthorityType))callback;
- (ZLPhotoConfiguration * _Nonnull)customAlertWhenNoAuthority:(void (^ _Nullable)(enum ZLNoAuthorityType))callback;
@end

@class NSURL;
@class ZLPhotoModel;
@class NSData;
@class PHLivePhoto;
@class AVPlayerItem;
@class AVAsset;
@class NSString;
SWIFT_CLASS("_TtC14ZLPhotoBrowser14ZLPhotoManager")
@interface ZLPhotoManager : NSObject
/// Save video to album.
+ (void)saveVideoToAlbumWithUrl:(NSURL * _Nonnull)url completion:(void (^ _Nullable)(NSError * _Nullable, PHAsset * _Nullable))completion;
/// Fetch photos from result.
+ (NSArray<ZLPhotoModel *> * _Nonnull)fetchPhotoIn:(PHFetchResult<PHAsset *> * _Nonnull)result ascending:(BOOL)ascending allowSelectImage:(BOOL)allowSelectImage allowSelectVideo:(BOOL)allowSelectVideo limitCount:(NSInteger)limitCount SWIFT_WARN_UNUSED_RESULT;
/// Fetch all album list.
+ (void)getPhotoAlbumListWithAscending:(BOOL)ascending allowSelectImage:(BOOL)allowSelectImage allowSelectVideo:(BOOL)allowSelectVideo completion:(SWIFT_NOESCAPE void (^ _Nonnull)(NSArray<ZLAlbumListModel *> * _Nonnull))completion;
/// Fetch camera roll album.
+ (void)getCameraRollAlbumWithAllowSelectImage:(BOOL)allowSelectImage allowSelectVideo:(BOOL)allowSelectVideo completion:(void (^ _Nonnull)(ZLAlbumListModel * _Nonnull))completion;
/// Fetch asset data.
+ (PHImageRequestID)fetchOriginalImageDataFor:(PHAsset * _Nonnull)asset progress:(void (^ _Nullable)(CGFloat, NSError * _Nullable, BOOL * _Nonnull, NSDictionary * _Nullable))progress completion:(void (^ _Nonnull)(NSData * _Nonnull, NSDictionary * _Nullable, BOOL))completion;
+ (PHImageRequestID)fetchLivePhotoFor:(PHAsset * _Nonnull)asset completion:(void (^ _Nonnull)(PHLivePhoto * _Nullable, NSDictionary * _Nullable, BOOL))completion SWIFT_WARN_UNUSED_RESULT;
+ (PHImageRequestID)fetchVideoFor:(PHAsset * _Nonnull)asset progress:(void (^ _Nullable)(CGFloat, NSError * _Nullable, BOOL * _Nonnull, NSDictionary * _Nullable))progress completion:(void (^ _Nonnull)(AVPlayerItem * _Nullable, NSDictionary * _Nullable, BOOL))completion SWIFT_WARN_UNUSED_RESULT;
+ (PHImageRequestID)fetchAVAssetForVideo:(PHAsset * _Nonnull)asset completion:(void (^ _Nonnull)(AVAsset * _Nullable, NSDictionary * _Nullable))completion SWIFT_WARN_UNUSED_RESULT;
/// Fetch asset local file path.
/// note:
/// Asynchronously to fetch the file path. calls completionHandler block on the main queue.
+ (void)fetchAssetFilePathFor:(PHAsset * _Nonnull)asset completion:(void (^ _Nonnull)(NSString * _Nullable))completion;
/// Save asset original data to file url. Support save image and video.
/// note:
/// Asynchronously write to a local file. Calls completionHandler block on the main queue. If the asset object is in iCloud, it will be downloaded first and then written in the method. The timeout time is <code>ZLPhotoConfiguration.default().timeout</code>.
+ (void)saveAsset:(PHAsset * _Nonnull)asset toFile:(NSURL * _Nonnull)fileUrl completion:(void (^ _Nonnull)(NSError * _Nullable))completion;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ZLPhotoManager (SWIFT_EXTENSION(ZLPhotoBrowser))
+ (BOOL)hasPhotoLibratyReadWriteAuthority SWIFT_WARN_UNUSED_RESULT;
+ (BOOL)hasCameraAuthority SWIFT_WARN_UNUSED_RESULT;
+ (BOOL)hasMicrophoneAuthority SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS("_TtC14ZLPhotoBrowser12ZLPhotoModel")
@interface ZLPhotoModel : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class ZLResultModel;
SWIFT_CLASS("_TtC14ZLPhotoBrowser13ZLPhotoPicker")
@interface ZLPhotoPicker : NSObject
/// Success callback
/// block params
/// <ul>
///   <li>
///     params1: result models
///   </li>
///   <li>
///     params2: is full image
///   </li>
/// </ul>
@property (nonatomic, copy) void (^ _Nullable selectImageBlock)(NSArray<ZLResultModel *> * _Nonnull, BOOL);
/// Callback for photos that failed to parse
/// block params
/// <ul>
///   <li>
///     params1: failed assets.
///   </li>
///   <li>
///     params2: index for asset
///   </li>
/// </ul>
@property (nonatomic, copy) void (^ _Nullable selectImageRequestErrorBlock)(NSArray<PHAsset *> * _Nonnull, NSArray<NSNumber *> * _Nonnull);
@property (nonatomic, copy) void (^ _Nullable cancelBlock)(void);
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param selectedAssets preselected assets
///
- (nonnull instancetype)initWithSelectedAssets:(NSArray<PHAsset *> * _Nullable)selectedAssets;
/// Using this init method, you can continue editing the selected photo.
/// note:
///
/// If you want to continue the last edit, you need to satisfy the value of <code>saveNewImageAfterEdit</code> is <code>false</code> at the time of the last selection.
/// \param results preselected results
///
- (nonnull instancetype)initWithResults:(NSArray<ZLResultModel *> * _Nullable)results;
@end

SWIFT_CLASS("_TtC14ZLPhotoBrowser13ZLResultModel")
@interface ZLResultModel : NSObject
@property (nonatomic, readonly, strong) PHAsset * _Nonnull asset;
/// Whether the picture has been edited. Always false when <code>saveNewImageAfterEdit = true</code>.
@property (nonatomic, readonly) BOOL isEdited;
/// The order in which the user selects the models in the album. This index is not necessarily equal to the order of the model’s index in the array, as some PHAssets requests may fail.
@property (nonatomic, readonly) NSInteger index;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

typedef SWIFT_ENUM(NSInteger, ZLURLType, closed) {
  ZLURLTypeImage = 0,
  ZLURLTypeVideo = 1,
};

SWIFT_CLASS("_TtC14ZLPhotoBrowser14ZLVideoManager")
@interface ZLVideoManager : NSObject
/// 没有针对不同分辨率视频做处理，仅用于处理相机拍照的视频
+ (void)mergeVideosWithFileURLs:(NSArray<NSURL *> * _Nonnull)fileURLs completion:(void (^ _Nonnull)(NSURL * _Nullable, NSError * _Nullable))completion;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ZLVideoManager (SWIFT_EXTENSION(ZLPhotoBrowser))
@end

typedef SWIFT_ENUM(NSInteger, ExportType, closed) {
  ExportTypeMov = 0,
  ExportTypeMp4 = 1,
};

@interface ZLVideoManager (SWIFT_EXTENSION(ZLPhotoBrowser))
+ (void)exportVideoFor:(PHAsset * _Nonnull)asset exportType:(enum ExportType)exportType presetName:(NSString * _Nonnull)presetName complete:(void (^ _Nonnull)(NSURL * _Nullable, NSError * _Nullable))complete;
+ (void)exportVideoFor:(AVAsset * _Nonnull)asset range:(CMTimeRange)range exportType:(enum ExportType)exportType presetName:(NSString * _Nonnull)presetName complete:(void (^ _Nonnull)(NSURL * _Nullable, NSError * _Nullable))complete;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif
