---
path:            '/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1742265561000000000
    path:            '/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            72136
  - mtime:           1741406483000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1929664
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3888
    sdk_relative:    true
  - mtime:           1741407553000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741407560000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1099
    sdk_relative:    true
  - mtime:           1741407563000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1100
    sdk_relative:    true
  - mtime:           1741407555000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1511
    sdk_relative:    true
  - mtime:           1741407568000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            851
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15282
    sdk_relative:    true
  - mtime:           1741406651000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4262
    sdk_relative:    true
  - mtime:           1741407582000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18253
    sdk_relative:    true
version:         1
...
