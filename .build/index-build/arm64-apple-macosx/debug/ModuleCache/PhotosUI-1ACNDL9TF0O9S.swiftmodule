---
path:            '/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/PhotosUI.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1742265806000000000
    path:            '/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/PhotosUI.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            45148
  - mtime:           1741406483000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1929664
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741404978000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1741403049000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3888
    sdk_relative:    true
  - mtime:           1741407553000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741407560000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1099
    sdk_relative:    true
  - mtime:           1741407563000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1100
    sdk_relative:    true
  - mtime:           1741407555000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1511
    sdk_relative:    true
  - mtime:           1741407568000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            851
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15282
    sdk_relative:    true
  - mtime:           1741406651000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4262
    sdk_relative:    true
  - mtime:           1741407582000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18253
    sdk_relative:    true
  - mtime:           1741408093000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1741408144000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1741408692000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            167832
    sdk_relative:    true
  - mtime:           1741408664000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            6595
    sdk_relative:    true
  - mtime:           1741408850000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            57168
    sdk_relative:    true
  - mtime:           1741409051000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22857
    sdk_relative:    true
  - mtime:           1741748588000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1741409206000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3649
    sdk_relative:    true
  - mtime:           1742085903000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1741410019000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1741748427000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1741409002000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            33307
    sdk_relative:    true
  - mtime:           1741408100000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3489
    sdk_relative:    true
  - mtime:           1741408729000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            95502
    sdk_relative:    true
  - mtime:           1741752981000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            995833
    sdk_relative:    true
  - mtime:           1741753119000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            53546
    sdk_relative:    true
  - mtime:           1741411557000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            10918
    sdk_relative:    true
  - mtime:           1741410155000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1741410232000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            42734
    sdk_relative:    true
  - mtime:           1741413622000000000
    path:            'System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes'
    size:            1557
    sdk_relative:    true
  - mtime:           1741412492000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1742180978000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1738796627000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1741412646000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1741412759000000000
    path:            'System/Library/Frameworks/CoreAudioTypes.framework/Headers/CoreAudioTypes.apinotes'
    size:            1519
    sdk_relative:    true
  - mtime:           1741410621000000000
    path:            'System/Library/Frameworks/CoreMedia.framework/Headers/CoreMedia.apinotes'
    size:            65704
    sdk_relative:    true
  - mtime:           1741412488000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1741412803000000000
    path:            'System/Library/Frameworks/AudioToolbox.framework/Headers/AudioToolbox.apinotes'
    size:            114
    sdk_relative:    true
  - mtime:           1741837846000000000
    path:            'System/Library/Frameworks/MediaToolbox.framework/Headers/MediaToolbox.apinotes'
    size:            215
    sdk_relative:    true
  - mtime:           1742000253000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1741405100000000000
    path:            'System/Library/Frameworks/AVFAudio.framework/Headers/AVFAudio.apinotes'
    size:            8309
    sdk_relative:    true
  - mtime:           1741835937000000000
    path:            'System/Library/Frameworks/AVFoundation.framework/Headers/AVFoundation.apinotes'
    size:            98450
    sdk_relative:    true
  - mtime:           1741839244000000000
    path:            'System/Library/Frameworks/Photos.framework/Headers/Photos.apinotes'
    size:            308
    sdk_relative:    true
  - mtime:           1741410131000000000
    path:            'usr/lib/swift/CoreMIDI.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            14988
    sdk_relative:    true
  - mtime:           1741413361000000000
    path:            'usr/lib/swift/CoreAudio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            27389
    sdk_relative:    true
  - mtime:           1741410325000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            25175
    sdk_relative:    true
  - mtime:           1741411284000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            20608
    sdk_relative:    true
  - mtime:           1741412555000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1580
    sdk_relative:    true
  - mtime:           1741837641000000000
    path:            'usr/lib/swift/CoreMedia.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            135444
    sdk_relative:    true
  - mtime:           1741408694000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            229503
    sdk_relative:    true
  - mtime:           1741411783000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1810
    sdk_relative:    true
  - mtime:           1741837708000000000
    path:            'usr/lib/swift/AVFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            70387
    sdk_relative:    true
  - mtime:           1741411881000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            736
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1741409034000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            108063
    sdk_relative:    true
  - mtime:           1741414913000000000
    path:            'usr/lib/swift/CoreLocation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            14558
    sdk_relative:    true
  - mtime:           1741839362000000000
    path:            'System/Library/Frameworks/Photos.framework/Modules/Photos.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2588
    sdk_relative:    true
  - mtime:           1742182051000000000
    path:            'System/Library/Frameworks/AppKit.framework/Headers/AppKit.apinotes'
    size:            384123
    sdk_relative:    true
  - mtime:           1742182647000000000
    path:            'System/Library/Frameworks/MapKit.framework/Headers/MapKit.apinotes'
    size:            11481
    sdk_relative:    true
  - mtime:           1741838540000000000
    path:            'System/Library/Frameworks/PhotosUI.framework/Headers/PhotosUI.apinotes'
    size:            1025
    sdk_relative:    true
  - mtime:           1741411588000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21316
    sdk_relative:    true
  - mtime:           1741412206000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21594
    sdk_relative:    true
  - mtime:           1741413403000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22069
    sdk_relative:    true
  - mtime:           1741410142000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            734
    sdk_relative:    true
  - mtime:           1741410446000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1296
    sdk_relative:    true
  - mtime:           1742182199000000000
    path:            'System/Library/Frameworks/AppKit.framework/Modules/AppKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            91780
    sdk_relative:    true
  - mtime:           1741415554000000000
    path:            'usr/lib/swift/MapKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            2775
    sdk_relative:    true
  - mtime:           1741838653000000000
    path:            'System/Library/Frameworks/PhotosUI.framework/Modules/PhotosUI.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            8588
    sdk_relative:    true
version:         1
...
