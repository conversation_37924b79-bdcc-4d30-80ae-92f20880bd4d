client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module": ["<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module>"]
  "main": ["<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources"]
    description: "Write auxiliary file /Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources"

  "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/PrivacyInfo.xcprivacy":
    tool: copy-tool
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/PrivacyInfo.xcprivacy"]
    outputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/PrivacyInfo.xcprivacy"]
    description: "Copying /Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/PrivacyInfo.xcprivacy"

  "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/ZLPhotoBrowser.bundle":
    tool: copy-tool
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/ZLPhotoBrowser.bundle/"]
    outputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/ZLPhotoBrowser.bundle/"]
    description: "Copying /Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/ZLPhotoBrowser.bundle"

  "/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt"

  "<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules/ZLPhotoBrowser.swiftmodule"]
    outputs: ["<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module>"]

  "C.ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLClipImageDismissAnimatedTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLImagePreviewDismissInteractiveTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewAnimatedTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Animation/ZLPhotoPreviewPopInteractiveTransition.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Camera/ZLCustomCamera.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLAdjustSlider.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLBaseStickertState.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipImageViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLClipOverlayView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditImageViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditToolCells.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditVideoViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLEditorManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLFilter.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLImageStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLInputTextViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLPaths.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Edit/ZLTextStickerView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/AVCaptureDevice+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Array+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bool+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Bundle+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/CGFloat+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Cell+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/NSError+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHAsset+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/PHPhotoLibrary+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/Runtime+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/String+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIColor+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIFont+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIGraphicsImageRenderer+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIImage+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIScrollView+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIView+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/Extensions/UIViewController+ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAddPhotoCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAlbumListModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLAnimationUtils.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCameraConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCollectionViewFlowLayout.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLCustomAlertProtocol.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEditImageConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEmbedAlbumListView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLEnlargeButton.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLFetchImageOperation.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLGeneralDefine.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImageNavController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLImagePreviewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLLanguageDefine.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLNoAuthTipsView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoBrowser.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration+Chaining.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPicker.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoPreviewSheet.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration+Chaining.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLPhotoUIConfiguration.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressHUD.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLProgressView.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLResultModel.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailPhotoCell.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLThumbnailViewController.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLVideoManager.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/General/ZLWeakProxy.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/swift-version-2F0A5646E1D333AE.txt","<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources"]
    outputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules/ZLPhotoBrowser.swiftmodule"]
    description: "Compiling Swift Module 'ZLPhotoBrowser' (74 sources)"
    args: ["/Applications/Xcode-16.3.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ZLPhotoBrowser","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules/ZLPhotoBrowser.swiftmodule","-output-file-map","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/sources","-I","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j12","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser.build/ZLPhotoBrowser-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-F","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk","-Xcc","-F","-Xcc","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode-16.3.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Sources/","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Package.swift","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

  "ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/PrivacyInfo.xcprivacy","/Users/<USER>/ref_source/uikit/ZLPhotoBrowser/.build/index-build/arm64-apple-macosx/debug/ZLPhotoBrowser_ZLPhotoBrowser.bundle/ZLPhotoBrowser.bundle/"]
    outputs: ["<ZLPhotoBrowser-arm64-apple-macosx15.0-debug.module-resources>"]

