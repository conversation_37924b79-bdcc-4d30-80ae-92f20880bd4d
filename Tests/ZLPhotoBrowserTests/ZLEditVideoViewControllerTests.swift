import XCTest
import AVFoundation
@testable import ZLPhotoBrowser

final class ZLEditVideoViewControllerTests: XCTestCase {
    
    func testAutoLayoutConstraintsSetup() {
        // Create a test video asset
        guard let testVideoURL = Bundle.main.url(forResource: "test_video", withExtension: "mp4") else {
            // Create a dummy AVAsset for testing
            let testVideoURL = URL(string: "file:///test.mp4")!
            let asset = AVAsset(url: testVideoURL)
            performConstraintTest(with: asset)
            return
        }
        
        let asset = AVAsset(url: testVideoURL)
        performConstraintTest(with: asset)
    }
    
    private func performConstraintTest(with asset: AVAsset) {
        let editViewController = ZLEditVideoViewController(avAsset: asset, animateDismiss: false)
        
        // Load the view to trigger setupUI
        editViewController.loadViewIfNeeded()
        
        // Verify that translatesAutoresizingMaskIntoConstraints is disabled for all UI elements
        XCTAssertFalse(editViewController.value(forKey: "cancelBtn")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "doneBtn")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "collectionView")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "frameImageBorderView")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "leftSideView")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "rightSideView")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        XCTAssertFalse(editViewController.value(forKey: "indicator")?.value(forKey: "translatesAutoresizingMaskIntoConstraints") as? Bool ?? true)
        
        // Verify that constraint properties are not nil
        XCTAssertNotNil(editViewController.value(forKey: "leftSideViewLeadingConstraint"))
        XCTAssertNotNil(editViewController.value(forKey: "rightSideViewTrailingConstraint"))
        XCTAssertNotNil(editViewController.value(forKey: "frameImageBorderViewCenterXConstraint"))
        XCTAssertNotNil(editViewController.value(forKey: "frameImageBorderViewWidthConstraint"))
        XCTAssertNotNil(editViewController.value(forKey: "indicatorLeadingConstraint"))
        XCTAssertNotNil(editViewController.value(forKey: "indicatorWidthConstraint"))
    }
    
    func testLayoutAfterViewDidLayoutSubviews() {
        // Create a dummy AVAsset for testing
        let testVideoURL = URL(string: "file:///test.mp4")!
        let asset = AVAsset(url: testVideoURL)
        let editViewController = ZLEditVideoViewController(avAsset: asset, animateDismiss: false)
        
        // Load the view and trigger layout
        editViewController.loadViewIfNeeded()
        editViewController.view.frame = CGRect(x: 0, y: 0, width: 375, height: 812) // iPhone X size
        editViewController.viewDidLayoutSubviews()
        
        // Verify that the player layer has been positioned correctly
        let playerLayer = editViewController.value(forKey: "playerLayer") as? AVPlayerLayer
        XCTAssertNotNil(playerLayer)
        XCTAssertTrue(playerLayer!.frame.width > 0)
        XCTAssertTrue(playerLayer!.frame.height > 0)
        
        // Verify that the frame border view has correct valid rect
        let frameImageBorderView = editViewController.value(forKey: "frameImageBorderView") as? ZLEditVideoFrameImageBorderView
        XCTAssertNotNil(frameImageBorderView)
    }
    
    func testConstraintBasedPanGestures() {
        // Create a dummy AVAsset for testing
        let testVideoURL = URL(string: "file:///test.mp4")!
        let asset = AVAsset(url: testVideoURL)
        let editViewController = ZLEditVideoViewController(avAsset: asset, animateDismiss: false)
        
        // Load the view and trigger layout
        editViewController.loadViewIfNeeded()
        editViewController.view.frame = CGRect(x: 0, y: 0, width: 375, height: 812)
        editViewController.viewDidLayoutSubviews()
        
        // Get initial constraint values
        let leftConstraint = editViewController.value(forKey: "leftSideViewLeadingConstraint") as? NSLayoutConstraint
        let rightConstraint = editViewController.value(forKey: "rightSideViewTrailingConstraint") as? NSLayoutConstraint
        
        XCTAssertNotNil(leftConstraint)
        XCTAssertNotNil(rightConstraint)
        
        let initialLeftConstant = leftConstraint!.constant
        let initialRightConstant = rightConstraint!.constant
        
        // Simulate pan gesture on left side view
        let leftPan = editViewController.value(forKey: "leftSidePan") as? UIPanGestureRecognizer
        XCTAssertNotNil(leftPan)
        
        // Test that constraints can be modified (this would be done in actual pan gesture handling)
        leftConstraint!.constant = initialLeftConstant + 10
        rightConstraint!.constant = initialRightConstant - 5
        
        XCTAssertEqual(leftConstraint!.constant, initialLeftConstant + 10)
        XCTAssertEqual(rightConstraint!.constant, initialRightConstant - 5)
    }
    
    static var allTests = [
        ("testAutoLayoutConstraintsSetup", testAutoLayoutConstraintsSetup),
        ("testLayoutAfterViewDidLayoutSubviews", testLayoutAfterViewDidLayoutSubviews),
        ("testConstraintBasedPanGestures", testConstraintBasedPanGestures),
    ]
}
