// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		FDF45B4D2D94FCBB004CC01B /* ZLPhotoBrowser.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF45B4C2D94FCBB004CC01B /* ZLPhotoBrowser.framework */; };
		FDF45B4E2D94FCBB004CC01B /* ZLPhotoBrowser.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FDF45B4C2D94FCBB004CC01B /* ZLPhotoBrowser.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FDF45B572D94FD56004CC01B /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDF45B532D94FD56004CC01B /* ContentView.swift */; };
		FDF45B582D94FD56004CC01B /* PhotoPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDF45B542D94FD56004CC01B /* PhotoPicker.swift */; };
		FDF45B592D94FD56004CC01B /* SwiftUIExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDF45B552D94FD56004CC01B /* SwiftUIExampleApp.swift */; };
		FDF45B5A2D94FD56004CC01B /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FDF45B502D94FD56004CC01B /* Preview Assets.xcassets */; };
		FDF45B5B2D94FD56004CC01B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FDF45B522D94FD56004CC01B /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		FDF45B4F2D94FCBB004CC01B /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				FDF45B4E2D94FCBB004CC01B /* ZLPhotoBrowser.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		FDB678142D94F1A90026944F /* SwiftUIExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftUIExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FDF45B4C2D94FCBB004CC01B /* ZLPhotoBrowser.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ZLPhotoBrowser.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FDF45B502D94FD56004CC01B /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		FDF45B522D94FD56004CC01B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		FDF45B532D94FD56004CC01B /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		FDF45B542D94FD56004CC01B /* PhotoPicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoPicker.swift; sourceTree = "<group>"; };
		FDF45B552D94FD56004CC01B /* SwiftUIExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftUIExampleApp.swift; sourceTree = "<group>"; };
		FDF45B5C2D9525E2004CC01B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		FDB678112D94F1A90026944F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDF45B4D2D94FCBB004CC01B /* ZLPhotoBrowser.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FDB6780B2D94F1A90026944F = {
			isa = PBXGroup;
			children = (
				FDF45B562D94FD56004CC01B /* SwiftUIExample */,
				FDF45B4B2D94FCBB004CC01B /* Frameworks */,
				FDB678152D94F1A90026944F /* Products */,
			);
			sourceTree = "<group>";
		};
		FDB678152D94F1A90026944F /* Products */ = {
			isa = PBXGroup;
			children = (
				FDB678142D94F1A90026944F /* SwiftUIExample.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FDF45B4B2D94FCBB004CC01B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FDF45B4C2D94FCBB004CC01B /* ZLPhotoBrowser.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FDF45B512D94FD56004CC01B /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				FDF45B502D94FD56004CC01B /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		FDF45B562D94FD56004CC01B /* SwiftUIExample */ = {
			isa = PBXGroup;
			children = (
				FDF45B5C2D9525E2004CC01B /* Info.plist */,
				FDF45B512D94FD56004CC01B /* Preview Content */,
				FDF45B522D94FD56004CC01B /* Assets.xcassets */,
				FDF45B532D94FD56004CC01B /* ContentView.swift */,
				FDF45B542D94FD56004CC01B /* PhotoPicker.swift */,
				FDF45B552D94FD56004CC01B /* SwiftUIExampleApp.swift */,
			);
			path = SwiftUIExample;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FDB678132D94F1A90026944F /* SwiftUIExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDB678222D94F1AA0026944F /* Build configuration list for PBXNativeTarget "SwiftUIExample" */;
			buildPhases = (
				FDB678102D94F1A90026944F /* Sources */,
				FDB678112D94F1A90026944F /* Frameworks */,
				FDB678122D94F1A90026944F /* Resources */,
				FDF45B4F2D94FCBB004CC01B /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftUIExample;
			packageProductDependencies = (
			);
			productName = SwiftUIExample;
			productReference = FDB678142D94F1A90026944F /* SwiftUIExample.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FDB6780C2D94F1A90026944F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					FDB678132D94F1A90026944F = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = FDB6780F2D94F1A90026944F /* Build configuration list for PBXProject "SwiftUIExample" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = FDB6780B2D94F1A90026944F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = FDB678152D94F1A90026944F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FDB678132D94F1A90026944F /* SwiftUIExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FDB678122D94F1A90026944F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDF45B5A2D94FD56004CC01B /* Preview Assets.xcassets in Resources */,
				FDF45B5B2D94FD56004CC01B /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FDB678102D94F1A90026944F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDF45B572D94FD56004CC01B /* ContentView.swift in Sources */,
				FDF45B582D94FD56004CC01B /* PhotoPicker.swift in Sources */,
				FDF45B592D94FD56004CC01B /* SwiftUIExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		FDB678202D94F1AA0026944F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FDB678212D94F1AA0026944F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FDB678232D94F1AA0026944F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftUIExample/Preview Content\"";
				DEVELOPMENT_TEAM = 39Y6A562BB;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SwiftUIExample/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要相机权限";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "需要麦克风权限";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要写入相册权限";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册权限";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ZL.Example10;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FDB678242D94F1AA0026944F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftUIExample/Preview Content\"";
				DEVELOPMENT_TEAM = 39Y6A562BB;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SwiftUIExample/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要相机权限";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "需要麦克风权限";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要写入相册权限";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册权限";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ZL.Example10;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FDB6780F2D94F1A90026944F /* Build configuration list for PBXProject "SwiftUIExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDB678202D94F1AA0026944F /* Debug */,
				FDB678212D94F1AA0026944F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDB678222D94F1AA0026944F /* Build configuration list for PBXNativeTarget "SwiftUIExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDB678232D94F1AA0026944F /* Debug */,
				FDB678242D94F1AA0026944F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = FDB6780C2D94F1A90026944F /* Project object */;
}
